import { $ as $post } from "./axios.251be52e.js";
import { a1 as defineStore, s as shallowRef } from "./bootstrap.ab073eb8.js";
import { l as logger, e as setBusinessInfo } from "./index.6b739ea4.js";
const sysUrl = "/zhhb-system";
function deviceBindClass(data) {
  return $post(`${sysUrl}/v1/device/binding`, data);
}
function getStaffRoleList(data = {}) {
  return $post(`${sysUrl}/v1/staff/get_role_lists`, data);
}
function getDeviceBindClassInfo(serialNumber) {
  return $post(`${sysUrl}/v1/device/binding/detail`, {
    serialNumber
  });
}
const useDeviceStore = defineStore("zhhb-device", {
  state: () => ({
    /** 设备序列号 */
    serialNumber: null,
    /** 绑定的班级信息 */
    classInfo: null,
    /** 媒体设备列表 麦克风、摄像头等 */
    mediaDevices: []
  }),
  getters: {
    /**
     * 是否已绑定班级
     * @returns 是否已绑定班级
     */
    isBindClass() {
      return !!this.classInfo?.saasClassId;
    }
  },
  actions: {
    /**
     * 获取设备序列号
     * @returns 设备序列号
     */
    async getDeviceSerialNumber() {
      if (typeof window.api.getDeviceId !== "function") {
        throw new Error("【Device Store】获取设备号失败，getDeviceId未初始化");
      }
      if (this.serialNumber) {
        return this.serialNumber;
      }
      const res = await window.api.getDeviceId();
      logger.info("【Device Store】获取设备号响应结果：", res);
      if (Array.isArray(res) && res.length > 0) {
        const _serialNumber = res[0].deviceId;
        if (_serialNumber) {
          this.serialNumber = _serialNumber;
          return _serialNumber;
        }
      }
      throw new Error("【Device Store】获取设备号失败，序列号为空");
    },
    /**
     * 获取设备绑定信息失败
     */
    async loadBindClassInfo() {
      try {
        const serialNumber = await this.getDeviceSerialNumber();
        const { data } = await getDeviceBindClassInfo(serialNumber);
        const classInfo = data?.bindingList[0];
        if (classInfo) {
          this.classInfo = {
            saasSchoolId: classInfo.saasSchoolId,
            saasSchoolName: classInfo.saasSchoolName,
            saasClassId: classInfo.saasClassId,
            saasClassName: classInfo.saasClassName,
            saasCampusId: classInfo.saasCampusId,
            saasCampusName: classInfo.saasCampusName
          };
        } else {
          this.unbindClass();
        }
      } catch (e) {
        logger.error("【ClassBind】获取设备绑定信息失败：", e);
      }
    },
    /**
     * 绑定班级
     * @param classInfo 班级信息
     */
    bindClass(classInfo) {
      this.classInfo = classInfo;
    },
    /**
     * 解绑班级
     */
    unbindClass() {
      this.classInfo = null;
    },
    // 设置媒体设备 麦克风、摄像头等
    setMediaDevices(mediaDevices) {
      this.mediaDevices = mediaDevices;
    }
  },
  persist: true
});
const useClassroomStore = defineStore("zhhb-classroom", {
  state: () => ({
    /** 打开的资源列表 */
    openResourceList: shallowRef([]),
    /** 当前打开资源索引 */
    currentResourceIndex: 0,
    /** 当前上课老师信息 */
    classTeacher: shallowRef({
      saasUserId: "",
      saasSchoolId: "",
      saasCampusId: "",
      saasClassId: "",
      saasClassName: "",
      userName: "",
      avatar: "",
      saasSubjectId: "",
      saasSubjectName: "",
      saasSubjectCode: "",
      lessonId: "",
      // 判断老师是不是按课表上课
      disciplineId: "",
      disciplineName: "",
      disciplineCode: ""
    })
  }),
  getters: {
    /**
     * 当前资源
     * @param state 状态
     */
    currentResource(state) {
      return state.openResourceList[state.currentResourceIndex];
    }
  },
  actions: {
    /** 设置上课教师信息 */
    setTeacher(teacher) {
      const { saasSchoolId, saasUserId, saasSubjectCode } = this.classTeacher;
      if (saasSchoolId && saasUserId && saasSubjectCode) {
        if (saasSchoolId !== teacher.saasSchoolId || saasUserId !== teacher.saasUserId || saasSubjectCode !== teacher.saasSubjectCode) {
          this.clearResourceCache();
        }
      } else {
        this.clearResourceCache();
      }
      this.classTeacher = teacher;
    },
    /**
     * 清空上课教师信息
     */
    clearTeacher() {
      this.classTeacher = {
        saasUserId: "",
        saasSchoolId: "",
        saasCampusId: "",
        saasClassId: "",
        saasClassName: "",
        userName: "",
        avatar: "",
        saasSubjectId: "",
        saasSubjectName: "",
        saasSubjectCode: "",
        lessonId: "",
        disciplineId: "",
        disciplineName: "",
        disciplineCode: ""
      };
    },
    /**
     * 开始上课,缓存业务信息
     */
    startClass() {
      const { classTeacher } = this;
      setBusinessInfo({
        schoolId: classTeacher.saasSchoolId,
        campusId: classTeacher.saasCampusId,
        classId: classTeacher.saasClassId,
        className: classTeacher.saasClassName,
        subjectCode: classTeacher.saasSubjectCode,
        subjectName: classTeacher.saasSubjectName,
        userId: classTeacher.saasUserId
      });
    },
    /**
     * 下课,清除业务信息
     */
    endClass() {
      setBusinessInfo({});
    },
    /**
     * 更新当前资源索引
     */
    updateCurrentResourceIndex(index) {
      this.currentResourceIndex = index;
    },
    /**
     * 清空资源缓存
     */
    clearResourceCache() {
      this.openResourceList = [];
      this.currentResourceIndex = 0;
    },
    /**
     * 是否有绘制数据
     */
    hasDrawData() {
      const { openResourceList } = this;
      if (!openResourceList.length) {
        return false;
      }
      return openResourceList.some((item) => {
        return item.pages.some((page) => {
          return !!page.wbHistory[page.wbHistoryCurrentIndex];
        });
      });
    }
  },
  persist: {
    paths: ["classTeacher"]
  }
});
export {
  useClassroomStore as a,
  deviceBindClass as d,
  getStaffRoleList as g,
  useDeviceStore as u
};
