/* empty css                            */import { S as Swiper, P as Pagination, a as SwiperSlide, b as Swiper$1 } from "./swiper-slide.69c69280.js";
import { d as defineComponent, b as openBlock, m as createElementBlock, j as createBaseVNode, H as toDisplayString, I as withModifiers, a3 as useRouter, r as ref, w as watch, p as createVNode, f as withCtx, F as Fragment, R as renderList, u as unref, e as createBlock } from "./bootstrap.ab073eb8.js";
import { o as openSystemApp } from "./addon.77229f46.js";
import { s as showError } from "./toast.eecc13ca.js";
import { _ as _export_sfc } from "./index.6b739ea4.js";
import { u as useStartMenuApps } from "./startMenuApps.09556d11.js";
import "./index.cbb7d686.js";
import "./encryptlong.f30353e7.js";
import "./base.649d38c6.js";
const _hoisted_1$1 = ["src"];
const _hoisted_2 = ["title"];
const _sfc_main$1 = /* @__PURE__ */ defineComponent({
  __name: "index",
  props: {
    appItem: {}
  },
  setup(__props) {
    const onOpenApp = async (item) => {
      try {
        await openSystemApp(item);
      } catch (e) {
        showError("应用打开失败");
      }
    };
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", {
        class: "apps-item flex flex-v flex-ac flex-jc",
        onClick: _cache[0] || (_cache[0] = withModifiers(($event) => onOpenApp(_ctx.appItem), ["stop"]))
      }, [
        createBaseVNode("img", {
          class: "apps-item__icon",
          src: _ctx.appItem.app.data.icon
        }, null, 8, _hoisted_1$1),
        createBaseVNode("div", {
          class: "apps-item__name text-ellipsis",
          title: _ctx.appItem.app.name
        }, toDisplayString(_ctx.appItem.app.name), 9, _hoisted_2)
      ]);
    };
  }
});
const index_vue_vue_type_style_index_0_scoped_82cca89b_lang = "";
const AppItem = /* @__PURE__ */ _export_sfc(_sfc_main$1, [["__scopeId", "data-v-82cca89b"]]);
const _hoisted_1 = { class: "apps-list flex flex-wrap" };
const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "index",
  props: {
    isBackHome: {
      type: Boolean,
      default: true
    }
  },
  emits: ["close"],
  setup(__props, { emit: __emit }) {
    const emits = __emit;
    const { apps } = useStartMenuApps();
    const router = useRouter();
    Swiper.use([Pagination]);
    const appsList = ref([]);
    const onClickBackground = () => {
      if (__props.isBackHome) {
        router.replace("/workbench/home");
      } else {
        emits("close");
      }
    };
    watch(
      apps,
      (newVal) => {
        const list = [];
        newVal.forEach((item, index2) => {
          const outerIndex = Math.floor(index2 / 32);
          if (!list[outerIndex]) {
            list[outerIndex] = [];
          }
          const outerList = list[outerIndex];
          outerList.push(item);
        });
        appsList.value = list;
      },
      {
        immediate: true
      }
    );
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", { onClick: onClickBackground }, [
        createVNode(unref(Swiper$1), {
          pagination: "",
          class: "apps-center"
        }, {
          default: withCtx(() => [
            (openBlock(true), createElementBlock(Fragment, null, renderList(appsList.value, (list) => {
              return openBlock(), createBlock(unref(SwiperSlide), null, {
                default: withCtx(() => [
                  createBaseVNode("div", _hoisted_1, [
                    (openBlock(true), createElementBlock(Fragment, null, renderList(list, (item) => {
                      return openBlock(), createBlock(AppItem, {
                        key: item.app.name,
                        "app-item": item
                      }, null, 8, ["app-item"]);
                    }), 128))
                  ])
                ]),
                _: 2
              }, 1024);
            }), 256))
          ]),
          _: 1
        })
      ]);
    };
  }
});
const index_vue_vue_type_style_index_0_lang = "";
const index_vue_vue_type_style_index_1_scoped_31468b95_lang = "";
const index = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-31468b95"]]);
export {
  index as default
};
